1.QHYCFW_2.ino 是客户修改的程序 增加了一个功能 和大篇幅修改了程序
2.arduino 应该使用1.0.6版本 而不是最新版 (最新版没有board =Ard<PERSON><PERSON> Duemilan<PERSON> w/ ATmega328）

3. 原版board   atmega328.build.f_cpu=16000000L，16M。我们的是8M晶振 所以应该是要修改，否则运行会慢？（原理？）


##############################################################

atmega328.name=Ard<PERSON>o Duemilanove w/ ATmega328

atmega328.upload.protocol=arduino
atmega328.upload.maximum_size=30720
atmega328.upload.speed=57600

atmega328.bootloader.low_fuses=0xFF
atmega328.bootloader.high_fuses=0xDA
atmega328.bootloader.extended_fuses=0x05
atmega328.bootloader.path=atmega
atmega328.bootloader.file=ATmegaBOOT_168_atmega328.hex
atmega328.bootloader.unlock_bits=0x3F
atmega328.bootloader.lock_bits=0x0F

atmega328.build.mcu=atmega328p
atmega328.build.f_cpu=8000000L
atmega328.build.core=arduino
atmega328.build.variant=standard

##############################################################