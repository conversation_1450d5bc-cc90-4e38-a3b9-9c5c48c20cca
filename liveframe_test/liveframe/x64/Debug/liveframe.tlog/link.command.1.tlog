^C:\USERS\<USER>\DESKTOP\LIVEFRAME - 0208\LIVEFRAME\X64\DEBUG\LIVEFRAME.OBJ
/OUT:"C:\USERS\<USER>\DESKTOP\LIVEFRAME - 0208\X64\DEBUG\LIVEFRAME.EXE" /INCREMENTAL /NOLOGO QHYCCD_X64.LIB KERNEL32.LIB USER32.LIB GDI32.LIB WINSPOOL.LIB COMDLG32.LIB ADVAPI32.LIB SHELL32.LIB OLE32.LIB OLEAUT32.LIB UUID.LIB ODBC32.LIB ODBCCP32.LIB /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG:FASTLINK /PDB:"C:\USERS\<USER>\DESKTOP\LIVEFRAME - 0208\X64\DEBUG\LIVEFRAME.PDB" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:\USERS\<USER>\DESKTOP\LIVEFRAME - 0208\X64\DEBUG\LIVEFRAME.LIB" /MACHINE:X64 X64\DEBUG\LIVEFRAME.OBJ
