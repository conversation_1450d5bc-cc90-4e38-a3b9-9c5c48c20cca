// liveframe.cpp : 此文件包含 "main" 函数。程序执行将在此处开始并结束。
//


#include <time.h>
#include <windows.h>
#include <stdio.h>
#include <iostream>
#include <fstream>
#include "qhyccd.h"



using namespace std;


#define BIN1X1MODE


int curCFWPort=0;


bool SetCFWPort(qhyccd_handle* camhandle,int port)
{
	int ret = SetQHYCCDParam(camhandle, CONTROL_CFWPORT, port);
	if (ret == QHYCCD_SUCCESS) {
		printf("转轮port设置成功！\n");
		return true;
	}
	else {
		printf("转轮port设置失败！\n");
		return false;

	}
}




bool SetCFWPortAndWait(qhyccd_handle* camhandle,int port)
{
	bool result = false;
	clock_t clockBegin, clockEnd;
	clockBegin = clock();
	printf("进入SetCFWPortAndWait时，设置的位置=%d  现在的位置=%d \n", port, curCFWPort);
	clockEnd = clock();
	if (curCFWPort == port) return true;
	else if (!SetCFWPort(camhandle,port)) {
		printf("SetCFWPortAndWait   XXXXXXXXXXXXXX！\n");
		return false;
	}
	clockEnd = clock();
	long diffts = clockEnd - clockBegin;
	while (diffts < 120000 && curCFWPort != port)
	{
		int newPort = (int)GetQHYCCDParam(camhandle, CONTROL_CFWPORT);
		if (newPort == port) {
			printf("进入循环判断时，设置的位置=%d  现在的位置=%d \n", port, newPort);
			curCFWPort = port;
			printf("进入循环判断时，更新的现在的位置=%d \n", curCFWPort);
			result = true;
			printf("到达位置和预设位置一致！\n");
			break;
		}
		else {
			printf("现在位置newPort=%d \n", newPort);
			clockEnd = clock();
			diffts = clockEnd - clockBegin;
		}
	}
	return result;
}






int main()
{


	int s = 0, num = 0, found = 0;
	int ret = QHYCCD_ERROR, ret_live = QHYCCD_ERROR;
	char id[32];
	unsigned int w, h, bpp;

	double chipw, chiph, pixelw, pixelh;
	qhyccd_handle* camhandle=nullptr;

	int lastRandom = 0;
	int small_index = 0;
	int big_index = 4;
	int integerList[] = { 0,1,2,2,1,0 };
	int last_index = 0;

//

	ret = InitQHYCCDResource();
	EnableQHYCCDMessage(true);
	if (ret == QHYCCD_SUCCESS)
	{
		printf("System Information::Init SDK success!\n");
	}
	else
	{
		goto failure;
	}
	num = ScanQHYCCD();
	if (num > 0)
	{
		printf("System Information::Found %d QHYCCD device.\n", num);
	}
	else
	{
		printf("System Information::Not Found QHYCCD device,please check the usblink or the power\n");
			goto failure;
	}
	for (int i = 0;i < num;i++)
	{
		ret = GetQHYCCDId(i, id);
		if (ret == QHYCCD_SUCCESS)
		{
			printf("Connected to the QHYCCD device.(id:%s)\n", id);
			found = 1;
			break;
		}
	}
	if (found == 1)
	{
		camhandle = OpenQHYCCD(id);
		if (camhandle != NULL)
		{
			printf("System Information::Open QHYCCD device success!\n");
		}
		else
		{
			printf("System Information::Open QHYCCD device failed!(%d)\n", ret);
			goto failure;
		}

		//先单帧模式
		ret = SetQHYCCDStreamMode(camhandle, 0);
		if (ret == QHYCCD_SUCCESS)
		{
			printf("System Information::Set QHYCCD device stream mode success!\n");
		}
		else
		{
			printf("System Information::Set QHYCCD device stream mode failed!(%d)\n", ret);
			goto failure;
		}



		ret = InitQHYCCD(camhandle);
		if (ret == QHYCCD_SUCCESS)
		{
			printf("System Information::Init QHYCCD device success!\n");
		}
		else
		{
			printf("System Information::Init QHYCCD device failed!(%d)\n", ret);
			goto failure;
		}
		ret = GetQHYCCDChipInfo(camhandle, &chipw, &chiph, &w, &h, &pixelw, &pixelh, &bpp);
		if (ret == QHYCCD_SUCCESS)
		{
			printf("System Information::Get QHYCCD ChipInfo success!\n");
			printf(" CCD/CMOS chip information :\n");
			printf(" CCD/CMOS chip width :%3f mm\n", chipw);
			printf(" CCD/CMOS chip height :%3f mm\n", chiph);
			printf(" CCD/CMOS chip pixel width :%3f um\n", pixelw);
			printf(" CCD/CMOS chip pixel height :%3f um\n", pixelh);
			printf(" CCD/CMOS chip width :%d\n", w);
			printf(" CCD/CMOS chip height :%d\n", h);
			printf(" CCD/CMOS chip depth :%d\n", bpp);
		}
		else
		{
			printf("System Information::Get QHYCCD ChipInfo failed!(%d)\n", ret);
			goto failure;
		}
		
		
		
	}

	else
	{
		printf("The camera is not QHYCCD or other error \n");
		goto failure;
	}



	// Providing a seed value
	srand((unsigned)time(NULL));


	for (int i = 0; i >= 0; i++)
	{
		int port = i % 10;
		//int random = rand() % 4;
		int random = rand() % 10;
		port = random;

		//port = random;
		//port = i % 4;
		//port = integerList[i%6];

		/*if (i%5==0)
		{
			port = lastRandom;
		}*/
	
		/*port = port % 4;
		switch (port)
		{
		case 0:
			port = 0;
			break;
		case 1:
			port = 4;
			break;
		case 2:
			port = 5;
			break;
		case 3:
			port = 9;
			break;
		default:
			break;
		}*/

		//port = i % 6;

		//switch (port)
		//{
		//case 0:
		//	port = 1;
		//	break;
		//case 1:
		//	port = 0;
		//	break;
		//case 2:
		//	port = 4;
		//	break;
		//case 3:
		//	port = 6;
		//	break;
		//case 4:
		//	port = 5;
		//	break;
		//case 5:
		//	port = 9;
		//	break;
		//default:
		//	break;
		//}
		
		lastRandom = random;
		printf("SetCFWPortAndWait  [ %d ]=====>[ %d ] ----------------[%d] \n", last_index, port,i);
		if (SetCFWPortAndWait(camhandle,port + 0x30)) {
			printf(" OK   OK   OK   OK   OK  [ %d ]=====>[ %d ]\n", last_index , port);
			last_index = port;
		}
		else {
			printf("XXXXXXXXXXXXXXXXXXXX [%d]====>[ %d ] \n", last_index, port);
		}
		printf("SetCFWPortAndWait  [ %d ]=====>[ %d ] ----------------[%d] \n", last_index, port,i);
		Sleep(500);

	}




//------------------------

	ret = CloseQHYCCD(camhandle);
	if (ret == QHYCCD_SUCCESS) {
		printf("Close camera success!\n");
	}
	else {
		printf("Close camera fail!\n");
		goto failure;
	
		}



	ret = ReleaseQHYCCDResource();
	if (ret == QHYCCD_SUCCESS)
	{
		printf("Rlease SDK Resource success!\n");
	}
	else
	{
		goto failure;
	}

	

system("pause");
return 0;
failure:
printf("some fatal error happened\n");
return 1;
}



// 运行程序: Ctrl + F5 或调试 >“开始执行(不调试)”菜单
// 调试程序: F5 或调试 >“开始调试”菜单

// 入门使用技巧: 
//   1. 使用解决方案资源管理器窗口添加/管理文件
//   2. 使用团队资源管理器窗口连接到源代码管理
//   3. 使用输出窗口查看生成输出和其他消息
//   4. 使用错误列表窗口查看错误
//   5. 转到“项目”>“添加新项”以创建新的代码文件，或转到“项目”>“添加现有项”以将现有代码文件添加到项目
//   6. 将来，若要再次打开此项目，请转到“文件”>“打开”>“项目”并选择 .sln 文件
