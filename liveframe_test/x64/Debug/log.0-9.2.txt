System Information::Init SDK success!
System Information::Found 1 QHYCCD device.
Connected to the QHYCCD device.(id:QHY600M-bf145d4d958fbe41)
System Information::Open QHYCCD device success!
System Information::Set QHYCCD device stream mode success!
System Information::Init QHYCCD device success!
System Information::Get QHYCCD ChipInfo success!
 CCD/CMOS chip information :
 CCD/CMOS chip width :36.096000 mm
 CCD/CMOS chip height :24.146720 mm
 CCD/CMOS chip pixel width :3.760000 um
 CCD/CMOS chip pixel height :3.760000 um
 CCD/CMOS chip width :9600
 CCD/CMOS chip height :6422
 CCD/CMOS chip depth :16
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=0 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=57  现在的位置=57 
进入循环判断时，更新的现在的位置=57 
到达位置和预设位置一致！
SetCFWPortAndWait 9 成功!
进入SetCFWPortAndWait时，设置的位置=48  现在的位置=57 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=48  现在的位置=48 
进入循环判断时，更新的现在的位置=48 
到达位置和预设位置一致！
SetCFWPortAndWait 0 成功!
进入SetCFWPortAndWait时，设置的位置=49  现在的位置=48 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=49  现在的位置=49 
进入循环判断时，更新的现在的位置=49 
到达位置和预设位置一致！
SetCFWPortAndWait 1 成功!
进入SetCFWPortAndWait时，设置的位置=50  现在的位置=49 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=50  现在的位置=50 
进入循环判断时，更新的现在的位置=50 
到达位置和预设位置一致！
SetCFWPortAndWait 2 成功!
进入SetCFWPortAndWait时，设置的位置=51  现在的位置=50 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=51  现在的位置=51 
进入循环判断时，更新的现在的位置=51 
到达位置和预设位置一致！
SetCFWPortAndWait 3 成功!
进入SetCFWPortAndWait时，设置的位置=52  现在的位置=51 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=52  现在的位置=52 
进入循环判断时，更新的现在的位置=52 
到达位置和预设位置一致！
SetCFWPortAndWait 4 成功!
进入SetCFWPortAndWait时，设置的位置=53  现在的位置=52 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=53  现在的位置=53 
进入循环判断时，更新的现在的位置=53 
到达位置和预设位置一致！
SetCFWPortAndWait 5 成功!
进入SetCFWPortAndWait时，设置的位置=54  现在的位置=53 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=54  现在的位置=54 
进入循环判断时，更新的现在的位置=54 
到达位置和预设位置一致！
SetCFWPortAndWait 6 成功!
进入SetCFWPortAndWait时，设置的位置=55  现在的位置=54 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=55  现在的位置=55 
进入循环判断时，更新的现在的位置=55 
到达位置和预设位置一致！
SetCFWPortAndWait 7 成功!
进入SetCFWPortAndWait时，设置的位置=56  现在的位置=55 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
进入循环判断时，设置的位置=56  现在的位置=56 
进入循环判断时，更新的现在的位置=56 
到达位置和预设位置一致！
SetCFWPortAndWait 8 成功!
进入SetCFWPortAndWait时，设置的位置=57  现在的位置=56 
转轮port设置成功！
现在位置newPort=78 
现在位置newPort=78 
现在位置newPort=78 
