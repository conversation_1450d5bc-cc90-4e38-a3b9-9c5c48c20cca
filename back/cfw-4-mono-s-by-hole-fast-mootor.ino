//use visual studio 2019  plugin (visualmicro)   Arduino IDE for Visual Studio https://www.visualmicro.com/page/Arduino-Visual-Studio-Downloads.aspx (can upload code after add boards.txt and fix the F_CPU = 8M problem)
// arduino 1.8.15 (can not upload code due to boards.txt may not have upload.tool)
// 
//Need plugin accelstepper 1.61
#include <AccelStepper.h>
//Need plugin Arduino_JSON


#define interruputPin_A 0
#define interruputPin_B 1
#define lightPin_A 4
#define lightPin_B 4
#define maxHoleSize 16  //1.44deg  15 step
#define halfHoleSize 8  //1.44deg  15 step
#define maxStep 3592
#define maxStepSpeed 1700
#define moveAcceleration 1700 //need to check what limit the speed TODO is setMinPulseWidth?

#define minResetHoleGap 180 //12deg 120step = 100    20deg = 180
#define maxResetHoleGap 240 //12deg 120step = 140    20deg = 240
#define maxIngoreHoleGap 8 // may receive more than one interrupt during one hole

#define minIngoreHoleGap_SlewCheck 20 // Ignore reset hole and other interrupt during one hole

// Define stepper motor connections and motor interface type. Motor interface type must be set to 1 when using a driver:
#define dirPin_A 6
#define dirPin_B 5
#define stepPin_A 7
#define stepPin_B A3
//#define currentPin 8  // not used, not sure the plugin accelstepper can use it or not
#define motorInterfaceType 1
#define versionNum 20230629


#define enablePin_A A0
#define enablePin_B A1

#define ledPin_R 8
#define ledPin_G 9
#define ledPin_B 10

#define stable_wheel_ms 40

int lastIndext_now_cmd = -1;

volatile int holeCount = 0;
volatile int fwHoleCount = 0;
volatile int incomingByte = 0; // for incoming serial data
AccelStepper stepper = AccelStepper(motorInterfaceType, stepPin_A, dirPin_A);
long holePosition[maxHoleSize];
long fwHolePositionSort[maxHoleSize];
volatile long preInterruptPosition = -100;
volatile long preHolePosition = -100;
volatile long preGapStep = -1;
int meetResetGapCounter = 0;
int lastIndext = -1;
volatile int stop_after_hole_count_A=-1;
unsigned long preTime;
long nextTargetPosition = 0;
int pos_gap_count_A = 0;
int pos_hole_count_A = 0;
int run_dir_A = 1;



volatile int holeCount_B = 0;
volatile int fwHoleCount_B = 0;
AccelStepper stepper_B = AccelStepper(motorInterfaceType, stepPin_B, dirPin_B);
long holePosition_B[maxHoleSize];
long fwHolePositionSort_B[maxHoleSize];
volatile long preInterruptPosition_B = -100;
volatile long preHolePosition_B = -100;
volatile long preGapStep_B = -1;
int meetResetGapCounter_B = 0;
int lastIndext_B = -1;
volatile int stop_after_hole_count_B=-1;
long nextTargetPosition_B = 0;
int pos_gap_count_B = 0;
int pos_hole_count_B = 0;
int run_dir_B = 1;

volatile long holeCountSlewCheck_A=0;
volatile long preInterruptPositionSlewCheck_A = -100;
volatile int skip_first_hole_steps_A = 0;
volatile int skip_first_hole_steps_diff_A = 0;

volatile long holeCountSlewCheck_B=0;
volatile long preInterruptPositionSlewCheck_B = -100;
volatile int skip_first_hole_steps_B = 0;
volatile int skip_first_hole_steps_diff_B = 0;


volatile long holePositionDebug[maxHoleSize];
String cmdLine;

void setup() {
	pinMode(enablePin_A,OUTPUT);
    digitalWrite(enablePin_A,HIGH);
	pinMode(enablePin_B,OUTPUT);
    digitalWrite(enablePin_B,HIGH);
    // board type: atmega328.name=Arduino Duemilanove w/ ATmega328
    // our chip F_CPU is 8M not 16M as default, so you need to change boards.txt atmega328.build.f_cpu=8000000L
	Serial.setTimeout(10); //for serial fast response , change readString timeout
    Serial.begin(9600); // opens serial port, sets data rate to 9600 bps
    //while (!Serial);
    stepper.setMaxSpeed(maxStepSpeed);
    stepper.setMinPulseWidth(10);
	
    stepper_B.setMaxSpeed(maxStepSpeed);
    stepper_B.setMinPulseWidth(10);
	
    pinMode(lightPin_A,OUTPUT);


    pinMode(ledPin_R,OUTPUT);
    pinMode(ledPin_G,OUTPUT);
    pinMode(ledPin_B,OUTPUT);
	
	digitalWrite(ledPin_R,HIGH);
	digitalWrite(ledPin_G,HIGH);
	digitalWrite(ledPin_B,HIGH);

	
    delay(100);
    stepper.setPinsInverted(false,false,false);
    stepper.setAcceleration(moveAcceleration);
	
    stepper_B.setPinsInverted(false,false,false);
    stepper_B.setAcceleration(moveAcceleration);
    
	
    resetHole();    // find holes between 

	delay(300);

	//resetHole_B();
	

	//digitalWrite(ledPin_R,HIGH);
	//digitalWrite(ledPin_G,HIGH);
	//digitalWrite(ledPin_B,HIGH);
    
}


void calculate_short_path_and_dir_A(int startIdx, int endIdx){

	int speedAndDir = maxStepSpeed;
	run_dir_A = 1;
	bool pass_reset_hole = false;
	if(endIdx > startIdx){
		if(endIdx - startIdx > 2){
			run_dir_A = -1;
		    speedAndDir = -1 * speedAndDir;
			pos_gap_count_A = fwHoleCount - 1 - endIdx + startIdx;
			pass_reset_hole = true;
		}else{
			run_dir_A = 1;
			speedAndDir = speedAndDir;
			pos_gap_count_A = endIdx - startIdx;
			pass_reset_hole = false;
		}
	}else if(endIdx < startIdx){
		if(startIdx - endIdx > 2){
			run_dir_A = 1;
			speedAndDir = speedAndDir;
			pos_gap_count_A = fwHoleCount - 1 - startIdx + endIdx;
			pass_reset_hole = true;
		}else{
			run_dir_A = -1;
			speedAndDir = -1 * speedAndDir;
			pos_gap_count_A = startIdx - endIdx;
			pass_reset_hole = false;
		}
	}else{
		pos_gap_count_A = 0;
	}
	pos_hole_count_A = pos_gap_count_A + (pass_reset_hole? 1 : 0);
	//Serial.println(String(startIdx)  + " => " + endIdx );
	//Serial.println("c A  = "  + String(pass_reset_hole)  + " / " + pos_gap_count_A + " / " + pos_hole_count_A);
	stepper.setSpeed(speedAndDir);

}



void holeInterrupt_reset()
{
  digitalWrite(ledPin_B,LOW);
if(holeCount>0){ //filter interrupt after first hole interrupt
   if(stepper.currentPosition() - preInterruptPosition < maxIngoreHoleGap){
	   	digitalWrite(ledPin_B,HIGH);
		 //Serial.println("R   "+ String(stepper.currentPosition()) + " - " + String(preInterruptPosition)); 
       return;
   }
   preGapStep = stepper.currentPosition() - preInterruptPosition;
   
   if(minResetHoleGap < preGapStep && preGapStep < maxResetHoleGap){
       meetResetGapCounter++;
   }
}
 
  if(meetResetGapCounter == 1){
    fwHolePositionSort[fwHoleCount] = stepper.currentPosition() % maxStep;
    fwHoleCount = fwHoleCount + 1;
  }
   holeCount = holeCount + 1;
  preInterruptPosition = stepper.currentPosition();
  digitalWrite(ledPin_B,HIGH);
  //Serial.println("-" + String(meetResetGapCounter)); 
  if(meetResetGapCounter >= 2){
	nextTargetPosition =  stepper.currentPosition() + (halfHoleSize) ;
   
	
	
  }
  
}




void resetSlewCheck(){
    skip_first_hole_steps_A = stepper.currentPosition();
	holeCountSlewCheck_A = 0;
	preInterruptPositionSlewCheck_A = stepper.currentPosition();
    skip_first_hole_steps_B = stepper_B.currentPosition();
	holeCountSlewCheck_B = 0;
	preInterruptPositionSlewCheck_B = stepper_B.currentPosition();
}




void holeInterrupt_stop_at_hole()
{

//if(holeCountSlewCheck_A>0){ //filter interrupt after first hole interrupt
   if(abs(stepper.currentPosition() - preInterruptPositionSlewCheck_A) < minIngoreHoleGap_SlewCheck){
	   //digitalWrite(ledPin_R,HIGH);
       return;
   }
//}
	digitalWrite(ledPin_B,LOW);
  skip_first_hole_steps_diff_A = abs(skip_first_hole_steps_A - stepper.currentPosition());
  if(skip_first_hole_steps_diff_A > 20 ){   //走出大于一个孔位（小于一个reset间距）之后 开始计数，用于忽略当前孔位
    holeCountSlewCheck_A = holeCountSlewCheck_A + 1;
    preInterruptPositionSlewCheck_A = stepper.currentPosition();
	
  }else{
	  	//Serial.println(" K: " + String(abs(skip_first_hole_steps_A - stepper.currentPosition())));
	  	//Serial.println(" K: " + String(holeCountSlewCheck_A) + " >= " + String(pos_hole_count_A) );
  }
  
  if(holeCountSlewCheck_A >= pos_hole_count_A && holeCountSlewCheck_A >0){
    nextTargetPosition =  stepper.currentPosition()+ (halfHoleSize * run_dir_A);
    //stepper.stop();
	//stepper.runToPosition();
	//Serial.println(" s: " + String(abs(skip_first_hole_steps_A - stepper.currentPosition())));
	//Serial.println(" P: " + String(stepper.currentPosition()));
  }
  digitalWrite(ledPin_B,HIGH);
}


void resetHole(){
  digitalWrite(enablePin_A,LOW);
// Serial.println("=======================================2 "); //!!! too much output will cause arduino lock !!!
  detachInterrupt(interruputPin_A); // detach protential  holeInterrupt_slewCheck
  holeCount = 0;
  fwHoleCount = 0;
  meetResetGapCounter = 0;
  stepper.setCurrentPosition(0);
  preInterruptPosition = -100;

  attachInterrupt(interruputPin_A,holeInterrupt_reset,FALLING);
  digitalWrite(lightPin_A,HIGH);
  
  for (byte i = 0; i < maxHoleSize; i++) {
	  holePosition[i] = -1;
          fwHolePositionSort[i] = -1;
    }
    delay(200);
    
	stepper.setSpeed(maxStepSpeed);
    nextTargetPosition = maxStep * 3;
	
	while (stepper.currentPosition() != nextTargetPosition)
	{
		stepper.runSpeed();
	}
    
    digitalWrite(lightPin_A,LOW);
    detachInterrupt(interruputPin_A);
    
    lastIndext = 0;
	attachInterrupt(interruputPin_A,holeInterrupt_stop_at_hole,FALLING); 
	resetSlewCheck(); // reset slew check
    //Serial.print(0);
	delay(stable_wheel_ms); 
	digitalWrite(enablePin_A,HIGH);
	lastIndext_now_cmd = 0;
	Serial.print(0);
	
}

void resetHole_B(){

}


void loop() {

    if (Serial.available() > 0) {
        cmdLine = Serial.readString();
        //Serial.println(cmdLine);
		//Serial.println(" B 0 =>  "  + cmdLine);
        if (cmdLine == "version" || cmdLine == "VRS") {
			
            Serial.print(versionNum);
        }else if (cmdLine == "MXP") {
	      Serial.print(String(fwHoleCount -1));
        }else if (cmdLine == "NOW") {
	      Serial.print(String(lastIndext_now_cmd));
        }else if (cmdLine == "debug") {
          for(byte i = 0; i < maxHoleSize; i++){
             Serial.println(String(i) + "  pos = " + String(fwHolePositionSort[i]));	
          }
            Serial.println("****************");	
            //for(byte i = 0; i < maxHoleSize; i++){
            //     Serial.println(String(i) + " : " + String(holePosition[i]));	
            //}
          for(byte i = 0; i < maxHoleSize; i++){
             Serial.println(String(i) + "  -pos = " + String(fwHolePositionSort_B[i]));	
          }
            Serial.println("****************");	
            //for(byte i = 0; i < maxHoleSize; i++){
            //     Serial.println(String(i) + " -: " + String(holePosition_B[i]));	
            //}
//            Serial.println("--------------");	 
//            for(byte i = 0; i < maxHoleSize; i++){
//                 Serial.println(String(i) + "ingore : " + String(ignoreInterGap[i]));	
//            }
        }
        else if(cmdLine == "reset" || cmdLine == "RESET"){

          resetHole(); 
		  delay(300);
		  //resetHole_B();

        }
        if(cmdLine.length() == 1){
          //Serial.println("goto  = " + cmdLine + "  " + String(fwHolePositionSort[cmdLine.toInt()]));
          if(cmdLine.toInt()<0 || cmdLine.toInt()>15){
            Serial.println("Error Goto = " + cmdLine + "  " + String(fwHolePositionSort[cmdLine.toInt()]));
          }
	
          preTime = millis();
          int targetIndex = cmdLine.toInt();
		  
		 /**  
          int targetStepsDiff = fwHolePositionSort[targetIndex] - stepper.currentPosition();
          int absSteps = abs(fwHolePositionSort[targetIndex] - stepper.currentPosition());
		  if(absSteps < 8){
			  Serial.print(targetIndex);
			  return;
		  }
		 
          if(absSteps > (maxStep/2 + minResetHoleGap)){ // can add something like 10 or 100 or 0 or something else
            int moveValue = targetStepsDiff < 0 ? (maxStep - absSteps) : (-1 *(maxStep - absSteps));
            stepper.move(moveValue);
            stepper.runToPosition();
          }else{
            stepper.moveTo(fwHolePositionSort[cmdLine.toInt()]);
            stepper.runToPosition();
          }
		  **/
		  
            //stepper.moveTo(fwHolePositionSort[cmdLine.toInt()]);
            //stepper.runToPosition();
			
            //stepper_B.moveTo(fwHolePositionSort_B[cmdLine.toInt()]);
            //stepper_B.runToPosition();
			
		  resetSlewCheck();
		  digitalWrite(lightPin_A,HIGH);
		  //Serial.println(" B  1=>  "  + cmdLine);
		  //delay(10);
		  if(targetIndex<(fwHoleCount -1)){
			  //Serial.println("goto A  = "  + String(targetIndex));
            //digitalWrite(enablePin_A,LOW);
			calculate_short_path_and_dir_A(lastIndext, targetIndex);
		    nextTargetPosition = stepper.currentPosition() + (run_dir_A * pos_gap_count_A * 800);
			skip_first_hole_steps_A = stepper.currentPosition();
			//Serial.println("pass A  = "  + String(pos_hole_count_A)  + " / " + pos_gap_count_A  + " / " + nextTargetPosition );
			delay(10); //等待中断稳定

			if(run_dir_A>0){ //拆开写是为了循环效率高一点
				while (stepper.currentPosition() < nextTargetPosition)
				{				
					stepper.runSpeed();
				}	
			}else{
				while (stepper.currentPosition() > nextTargetPosition)
				{				
					stepper.runSpeed();
				}	
			}

			
			//Serial.println("E  = "  + String(stepper.currentPosition()));
			lastIndext = targetIndex;
			lastIndext_now_cmd = lastIndext;
			
		  }
		  digitalWrite(lightPin_A,LOW);
          delay(stable_wheel_ms); 
		  
		  stepper_B.setCurrentPosition(0); //防止多圈超最大位置
		  stepper.setCurrentPosition(0);

		  Serial.print(cmdLine.toInt());
		  //digitalWrite(enablePin_B,HIGH);
		  //digitalWrite(enablePin_A,HIGH);
		  
//          Serial.println(millis() - preTime);

        }



    }
	
}