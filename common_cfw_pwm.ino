// pwm需要更换step 到pwm引脚 且 连接到int1引脚计数

#define interruputPin 0
#define lightPin 4

#define dirPin 6
#define stepPin 3
#define currentPin 8

#define led_B 9
#define led_G 11
#define led_R 10

#define motorInterfaceType 1
#define versionNum 20220129

volatile int holeCount = 0;
volatile int fwHoleCount = 0;
volatile int incomingByte = 0; // for incoming serial data



int lastIndext = -1;
unsigned long preTime;

volatile int holeCountSlewCheck=0;
volatile long preInterruptPositionSlewCheck = -100;

String cmdLine;
uint8_t index = 0;
uint8_t freq_values[] = {249, 199, 149, 99, 49};  // 对应频率：1kHz, 1.25kHz, 1.67kHz, 2.5kHz, 5kHz
const uint8_t max_index = sizeof(freq_values) / sizeof(freq_values[0]) - 1;
void setup() {

  Serial.setTimeout(10); 
  Serial.begin(9600);

  
    // 配置定时器2 - 快速PWM模式，TOP=OCR2A
    TCCR2A = (1 << COM2B1)  // 非反相模式OC2B
    | (1 << WGM21)   // WGM2[2:0] = 111 (模式7)
    | (1 << WGM20);  // 快速PWM，TOP=OCR2A
  TCCR2B = (1 << WGM22)   // 继续设置模式7
    | (1 << CS22);   // 预分频64 (时钟=16MHz时)

  // 初始频率设置 (约1kHz)
  OCR2A = 249;  // 频率 = 16e6 / (64 * (1 + 249)) = 1000Hz
  OCR2B = OCR2A / 2;  // 50%占空比



 

  pinMode(lightPin,OUTPUT);
  digitalWrite(lightPin,HIGH);

  // 新增PWM引脚初始化
  pinMode(stepPin, OUTPUT);
  analogWrite(stepPin, 200); 
  
  pinMode(led_B, OUTPUT);
  analogWrite(led_B, 250); 
  
  pinMode(led_G, OUTPUT);
  analogWrite(led_G, 250);
  
  pinMode(led_R, OUTPUT);
  analogWrite(led_R, 250); 

  delay(100);
  

}



void loop() {

          // 更新PWM频率
          OCR2A = freq_values[index];
          OCR2B = OCR2A / 2;  // 保持50%占空比
          
          // 循环遍历频率值
          index = (index >= max_index) ? 0 : index + 1;
          
          _delay_ms(10);  // 每秒切换一次频率
    if (Serial.available() > 0) {
        cmdLine = Serial.readString();
        if (cmdLine == "version" || cmdLine == "VRS") {
            Serial.print(versionNum);
        }else if (cmdLine == "MXP") {
        Serial.print(String(fwHoleCount -1));
        }else if (cmdLine == "NOW") {
        Serial.print(String(lastIndext));
        }else if (cmdLine == "RESET") {

        }else if (cmdLine == "debug") {

        }else if(cmdLine == "reset" || cmdLine == "RESET"){
        }
        

        if(cmdLine.length() == 1){

          preTime = millis();
        }



    }
}