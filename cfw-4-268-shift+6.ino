//use visual studio 2019  plugin (visualmicro)   Arduino IDE for Visual Studio https://www.visualmicro.com/page/Arduino-Visual-Studio-Downloads.aspx (can upload code after add boards.txt and fix the F_CPU = 8M problem)
// arduino 1.8.15 (can not upload code due to boards.txt may not have upload.tool)
// 
//Need plugin accelstepper 1.61
#include <AccelStepper.h>
//Need plugin Arduino_JSON


#define interruputPin 0
#define lightPin 4
#define maxHoleSize 16
#define maxStep 3200
#define maxStepSpeed 40000
#define moveAcceleration 12000 //need to check what limit the speed TODO is setMinPulseWidth?

#define minResetHoleGap 150
#define maxResetHoleGap 200
#define maxIngoreHoleGap 110 // may receive more than one interrupt during one hole

#define minIngoreHoleGap_SlewCheck 600 // Ignore reset hole and other interrupt during one hole

// Define stepper motor connections and motor interface type. Motor interface type must be set to 1 when using a driver:
#define dirPin 6
#define stepPin 7
#define currentPin 8  // not used, not sure the plugin accelstepper can use it or not
#define motorInterfaceType 1
#define versionNum 20240306

volatile int holeCount = 0;
volatile int fwHoleCount = 0;
volatile int incomingByte = 0; // for incoming serial data
AccelStepper stepper = AccelStepper(motorInterfaceType, stepPin, dirPin);
long holePosition[maxHoleSize];
long fwHolePositionSort[maxHoleSize];
volatile long preInterruptPosition = -100;
volatile long preHolePosition = -100;
volatile long preGapStep = -1;
int meetResetGapCounter = 0;
int lastIndext = -1;
unsigned long preTime;

volatile int holeCountSlewCheck=0;
volatile long preInterruptPositionSlewCheck = -100;


volatile long holePositionDebug[maxHoleSize];
String cmdLine;

void setup() {
    // board type: atmega328.name=Arduino Duemilanove w/ ATmega328
    // our chip F_CPU is 8M not 16M as default, so you need to change boards.txt atmega328.build.f_cpu=8000000L
	Serial.setTimeout(10); //for serial fast response , change readString timeout
    Serial.begin(9600); // opens serial port, sets data rate to 9600 bps
    //while (!Serial);
    stepper.setMaxSpeed(maxStepSpeed);
    stepper.setMinPulseWidth(10);
    pinMode(lightPin,OUTPUT);
//    digitalWrite(lightPin,HIGH);

	
    delay(100);
    stepper.setPinsInverted(true,false,false);
    stepper.setAcceleration(moveAcceleration);
    
    resetHole();    // find holes between 

    
}

void holeInterrupt_reset()
{
//   Serial.println("o");	
//holePositionDebug[ignoreInterGapIndex] = stepper.currentPosition();
if(holeCount>0){ //filter interrupt after first hole interrupt
   if(stepper.currentPosition() - preInterruptPosition < maxIngoreHoleGap){
       return;
   }
   preGapStep = stepper.currentPosition() - preInterruptPosition;
   //Serial.println("   Gap " + String(preGapStep) + "   " +String(stepper.currentPosition()) + " - " + String(preInterruptPosition));	
   if(minResetHoleGap < preGapStep && preGapStep < maxResetHoleGap){
       meetResetGapCounter++;
   }
}
 // Serial.println(String(stepper.currentPosition()) + " - " + String(holePosition[holeCount]) + "  " + String(holeCount) + " pre " + String(preInterruptPosition));	
  if(meetResetGapCounter == 1){
    fwHolePositionSort[fwHoleCount] = (stepper.currentPosition()+6) % maxStep;
    fwHoleCount = fwHoleCount + 1;
  }
   holeCount = holeCount + 1;
  preInterruptPosition = stepper.currentPosition();
  
}


void resetSlewCheck(){
	//Serial.println("x");	
	holeCountSlewCheck = 0;
	preInterruptPositionSlewCheck = -100;
}
void checkBeforeSlew(){
	digitalWrite(lightPin,HIGH);
	delay(10);
	resetSlewCheck();
}
void checkAfterSlew(){
	digitalWrite(lightPin,LOW);
	delay(10);
}
int calcShortCutHoleCount(int currentIndex,int targetIndex,int maxIndex){
	int smallIndex = currentIndex > targetIndex ? targetIndex : currentIndex;
	int largeIndex = currentIndex < targetIndex ? targetIndex : currentIndex;
	int pathA = largeIndex - smallIndex;
	int pathB = maxIndex - largeIndex + smallIndex + 1;
	return pathA < pathB? pathA : pathB;
}

bool checkHoleAfterSlew(){
	holeCountSlewCheck = 0;
	digitalWrite(lightPin,HIGH);
	delay(10);
	digitalWrite(lightPin,LOW);
	return true; //Sometimes it stops at the edge of the hole, and can not get enough light to trigger a signal
	//return holeCountSlewCheck > 0;
}

void holeInterrupt_slewCheck()
{
if(holeCountSlewCheck>0){ //filter interrupt after first hole interrupt
   if(abs(stepper.currentPosition() - preInterruptPositionSlewCheck) < minIngoreHoleGap_SlewCheck){
       return;
   }
}
  holeCountSlewCheck = holeCountSlewCheck + 1;
  preInterruptPositionSlewCheck = stepper.currentPosition();
  
}

void resetHole(){
// Serial.println("=======================================2 "); //!!! too much output will cause arduino lock !!!
  detachInterrupt(interruputPin); // detach protential  holeInterrupt_slewCheck
  holeCount = 0;
  fwHoleCount = 0;
  meetResetGapCounter = 0;
  attachInterrupt(interruputPin,holeInterrupt_reset,FALLING);
  digitalWrite(lightPin,HIGH);
  stepper.setCurrentPosition(0);
  for (byte i = 0; i < maxHoleSize; i++) {
	  holePosition[i] = -1;
          fwHolePositionSort[i] = -1;
    }
    delay(200);

    stepper.moveTo(maxStep * 3);
    stepper.runToPosition();
//    Serial.println(stepper.currentPosition());
//    stepper.moveTo(stepper.currentPosition() + (maxStep/2));
//    stepper.runToPosition();
    stepper.setCurrentPosition(stepper.currentPosition() % maxStep);
//    Serial.println(stepper.currentPosition());
//        stepper.moveTo(0);
//        stepper.runToPosition();


//    for(byte i = 0; i < maxHoleSize; i++){
//         Serial.println(String(i) + " : " + String(holePosition[i]));	
//    }
//    Serial.println("holeCount = " + String(holeCount));	
    
    digitalWrite(lightPin,LOW);
    detachInterrupt(interruputPin);
    
    stepper.moveTo(fwHolePositionSort[0]);
    stepper.runToPosition();
    lastIndext = 0;
	//attachInterrupt(interruputPin,holeInterrupt_slewCheck,FALLING); // for slew check
	//attachInterrupt(interruputPin,holeInterrupt_slewCheck,RISING); // for slew check //FIXME RISING is not able to check , because there may be more than two interrupt occure at one hole, pass two hole will give you 2-5 interrupt
	attachInterrupt(interruputPin,holeInterrupt_slewCheck,FALLING); // for slew check [use checkHoleAfterSlew] now can only check it a hole after slew.
	resetSlewCheck(); // reset slew check
    Serial.print(0);
}


void loop() {

    if (Serial.available() > 0) {
        cmdLine = Serial.readString();
//        Serial.println(cmdLine);
        if (cmdLine == "version" || cmdLine == "VRS") {
            Serial.print(versionNum);
        }else if (cmdLine == "MXP") {
	      Serial.print(String(fwHoleCount -1));
        }else if (cmdLine == "NOW") {
	      Serial.print(String(lastIndext));
        }else if (cmdLine == "RESET") {
               resetHole();
        }else if (cmdLine == "debug") {
          for(byte i = 0; i < maxHoleSize; i++){
             Serial.println(String(i) + "  pos = " + String(fwHolePositionSort[i]));	
          }
            Serial.println("****************");	
            for(byte i = 0; i < maxHoleSize; i++){
                 Serial.println(String(i) + " : " + String(holePosition[i]));	
            }
//            Serial.println("--------------");	 
//            for(byte i = 0; i < maxHoleSize; i++){
//                 Serial.println(String(i) + "ingore : " + String(ignoreInterGap[i]));	
//            }
        }
        else if(cmdLine == "reset" || cmdLine == "RESET"){
//          Serial.println(" current A = " + String(stepper.currentPosition()));
          resetHole(); 
//          Serial.println(" current B = " + String(stepper.currentPosition()));
//          Serial.println("holeInterrupt = " + String(holeCount));
        }
        
        else if(cmdLine == "r2"){
          resetHole(); 
//          for(byte i = 0; i < maxHoleSize; i++){
//             Serial.println(String(i) + "  pos = " + String(fwHolePositionSort[i]));	
//          }
        }
        if(cmdLine.length() == 1){
          //Serial.println("goto  = " + cmdLine + "  " + String(fwHolePositionSort[cmdLine.toInt()]));
          if(cmdLine.toInt()<0 || cmdLine.toInt()>15){
            Serial.println("Error Goto = " + cmdLine + "  " + String(fwHolePositionSort[cmdLine.toInt()]));
          }
		  //checkBeforeSlew();
          preTime = millis();
          int targetIndex = cmdLine.toInt();
          int targetStepsDiff = fwHolePositionSort[targetIndex] - stepper.currentPosition();
          int absSteps = abs(fwHolePositionSort[targetIndex] - stepper.currentPosition());
		  if(absSteps < 8){
			  Serial.print(targetIndex);
			  return;
		  }
          if(absSteps > (maxStep/2 + minResetHoleGap)){ // can add something like 10 or 100 or 0 or something else
//            Serial.println("to " + String(targetIndex));
//            Serial.println("gap" + String(absSteps) + " diff  " + String(targetStepsDiff) + "  sign " + String(signbit(targetStepsDiff)));
            int moveValue = targetStepsDiff < 0 ? (maxStep - absSteps) : (-1 *(maxStep - absSteps));
//            Serial.println("moveValue  " + String(moveValue));
            stepper.move(moveValue);
            stepper.runToPosition();
          }else{
            stepper.moveTo(fwHolePositionSort[cmdLine.toInt()]);
  //          Serial.println(fwHolePositionSort[cmdLine.toInt()]);
            stepper.runToPosition();
          }
		  //checkAfterSlew();
		  //Serial.println("check Hole count [" + String(holeCountSlewCheck) + "] [" + String((abs(targetIndex - lastIndext))%3 + 1));
		  //Serial.println("check Hole count [" + String(holeCountSlewCheck) + "] [" + String((abs(targetIndex - lastIndext))%3));
		  //Serial.println("check Hole count [" + String(holeCountSlewCheck) + "] [" + String(calcShortCutHoleCount(lastIndext,targetIndex,3)));
		  //if(holeCountSlewCheck != ((abs(targetIndex - lastIndext))%3 + 1)){  //check hole count(ignore reset hole) should be: ((abs(index diff)) mod(max hole index)) + 1 
		  //if(holeCountSlewCheck < ((abs(targetIndex - lastIndext))%3)){  //check hole count(ignore reset hole) should be: ((abs(index diff)) mod(max hole index)) + 1 
		  //if(holeCountSlewCheck < (calcShortCutHoleCount(lastIndext,targetIndex,3))){  //check hole count(ignore reset hole) should be: ((abs(index diff)) mod(max hole index)) + 1 
		  if(!checkHoleAfterSlew()){  //check hole count(ignore reset hole) should be: ((abs(index diff)) mod(max hole index)) + 1 
			  //targetIndex = 'N'; // set status to N if slew check is not pass, if this happens, filter wheel may need a reset
			  //print noting will cause command timeout in camera, print anything will cause success[camera firmware code bug]
			  //lastIndext = 'N'; // this will print 78, not the letter N 
		  }else{  // the hole interrupt may receive more than once during one hole, so ... presume it on the currect position when it have more interrupts than the holes
            Serial.print(targetIndex);
			lastIndext = cmdLine.toInt();
		  }
		  
//          Serial.println(millis() - preTime);

        }



    }
}