
int hole = 0 , state=0;
unsigned int i=0,j=0,t=0,m,n,k,sw,cc,s;                                    
String  readdata; 
unsigned char  optic_head;                    
unsigned short optic_value;                   
unsigned char  command;  

/*电机复位函数*/
void rst()                                            
{
    n = 1;                                            //打开中断
    i = 0;
    ccw(500);
    while(1) 
    {       
        cw(1);    
        if(t == 1)                                      //遇孔中断下降沿检测
        {
            t--;                                   
            while(1)
            {
               cw(1); 
               i++;                                      //孔后电机转动步数
               if(t == 1)                                //遇孔中断下降沿检测
               {  
                    t--;
                    Begin:
                    j = 0;
                    while(1)
                    {
                          cw(1);
                          j++;                       //再次记遇孔后电机转动步数
                          if(t == 1)                 //遇孔中断下降沿检测
                          {
                             t--;
                             if(((j < 2*i||j < i)&& sw == 1) || ((i < 2*j||i < j)&& sw == 0))        
                             { 
                                 i = j;
                                 goto Begin;
                              }
                              else 
                              {    
                                   cw(200);
                                   Stop();              
                                   return;
                              }                       
                           }
                     }    
                 }
            }
        }
    }
}

/*初始化函数*/        
void setup()                                          
{ 
      Serial.begin(19200);                      //波特率
      pinMode(4,OUTPUT);                         //光耦使能
      pinMode(5,INPUT_PULLUP);                   //串口控制指示
      pinMode(6,OUTPUT);                         //电机方向控制
      pinMode(7,OUTPUT);                         //电机驱动使能
      pinMode(8,OUTPUT);                         //电机驱动器电流控制
      pinMode(9,OUTPUT);                         //pin9、10、11为三色灯IO口
      pinMode(10,OUTPUT);
      pinMode(11,OUTPUT);
      pinMode(12,OUTPUT);                         //光耦接收
      pinMode(2,INPUT);                           //光耦中断IO
      pinMode(3,INPUT);                           //接收Q9信号
      pinMode(13,INPUT_PULLUP);                   //Q9相机使能
      attachInterrupt(0,counter,FALLING);         //调用下降沿中断命令 
}
/*中断执行函数*/ 
void counter()                                                 
{   
     t = 1;      //复位下降沿参量
     if(n == 1)                                   //中断使能
     {  
         if(sw == 1)          //正转
         {
             state++;                                    //色轮定位孔参量
             if(state == hole)                           //如果定位孔等于转盘光耦孔数，定位孔归零
             {    
                 state = 0;
             }
        }
        else if(sw == 0)     //反转
        {
           if(state == 0)
           {
               state = hole-1;
           }
           else 
           {
           state--;
           }
        }
        
     }
}
/*三色灯的状态*/
void setLED(int red,int green,int blue)
{
    analogWrite(9,red);
    analogWrite(10,green);
    analogWrite(11,blue);
}

void LED_flash()
{
    setLED(255,255,254);
    delay(100);
    setLED(255,254,255);
    delay(100);
    setLED(254,255,255);
    delay(100);
    setLED(254,255,254);
    delay(100);
    setLED(255,254,254);
    delay(100);
    setLED(254,254,255);
    delay(100);
    setLED(255,255,255);
}


/*电机掉电*/
void Stop()                                          
{
      digitalWrite(7,LOW);
      digitalWrite(8,HIGH);   //驱动器待机小电流
}

/*电机正转步数函数*/
void ccw(int Step)            //低速启动
{   
      digitalWrite(8,LOW);   //驱动器大电流
      while(Step--)                               
      {
            digitalWrite(7,LOW);
            delayMicroseconds(50);
            digitalWrite(7,HIGH);
            delayMicroseconds(50);
      }
}
void cw(int Step)         //高速工作                   
{    
      digitalWrite(8,LOW);   //驱动器大电流
      while(Step--)                               
      {
            digitalWrite(7,LOW);
            delayMicroseconds(30);
            digitalWrite(7,HIGH);
            delayMicroseconds(30);
      }
}

unsigned char MSB(unsigned int m)           //取高八位函数
{
      unsigned int n;
      n=(m&~0x00ff)>>8;
      return n;
}
unsigned char LSB(unsigned int m)         //取低八位函数
{
      unsigned int n;
      n=m&~0xff00;
      return n;
}
void delay_ms(unsigned int k)            //1ms延时函数
{
     while(k)
     {
       k --;
      delayMicroseconds(497);
     }      
}

/*QHY9相机控制*/
void Q9cam()
{
       while(digitalRead(13) == LOW)                                          
       {                
              optic_head=0;                                           
              optic_value=0; 
              while(digitalRead(3) == HIGH && digitalRead(13) == LOW){}
              for (i=0;i<200;i++)
              {
                	optic_head=optic_head+(!digitalRead(3));                 
                        delay_ms(1);            
               }    
                
               if (optic_head==200)
               {
                      	 while(digitalRead(3)==LOW){}
                      	 delay_ms(8);
                         delayMicroseconds(400);
                      	 for (i=0;i<16;i++)
                	 {    
                                 if(digitalRead(3) == HIGH)
                                 { 
                                              optic_value=(optic_value<<1)+1;
                                 }
                                 else
                                 {
                                              optic_value=(optic_value<<1);
                                 }
                              	 delay_ms(10);                        //10ms
                                 delayMicroseconds(300);              //0.6ms    
                         }                

                         if (MSB(optic_value)==0x5A)
                	 {           
                                 command=LSB(optic_value);
                                 if(((state - command) > 0 &&(state - command) < hole/2)||((command - state) > hole/2))    //正反转判断
                                 {
                                      digitalWrite(6,HIGH);          //反转
                                      sw = 0;
                                  }
                                  else
                                  {
                                      digitalWrite(6,HIGH);           //正转
                                      sw = 0;
                                  }
                                 while((command+1) < hole)
                                 {
                                             if(command ==0 && state != 0)           //在第1个滤镜位置停
                                             {
                                                               n = 0; ccw(500);                         //关中断 
                                                               digitalWrite(4,HIGH);           //开光耦灯
                                                               setLED(255,255,254);
                                                               n = 1;                           //开中断                                            
                                                               if(sw == 1)
                                                                {
                                                                    while(state!=0)                    
                                                                    {
                                                                        rst();
                                                                        state = 0;
                                                                    }
                                                                }
                                                                else if(sw == 0)
                                                                {
                                                                    while(state!=0) 
                                                                    {                                                 
                                                                          cw(1);   
                                                                    }
                                                                }
                                                               setLED(255,255,255);
                                                               digitalWrite(4,LOW);              //关光耦灯
                                                               break;
                                              }    
                                              else if(command ==1 && state != 1)          //在第2个滤镜位置停
                                              { 
                                                               n = 0; ccw(500);
                                                               digitalWrite(4,HIGH);
                                                               setLED(255,254,255);
                                                               n = 1;
                                                               while(state!=1)  
                                                               {   
                                                                 cw(1);  
                                                               }
                                                               cw(200);
                                                               Stop();
                                                               setLED(255,255,255);
                                                               digitalWrite(4,LOW);              //关光耦灯
                                                               break;
                                              }                                   
                                              else if(command ==2 && state != 2)           //在第3个滤镜位置停
                                              {
                                                               n = 0; ccw(500);
                                                               digitalWrite(4,HIGH);
                                                               setLED(254,255,255);
                                                               n = 1;
                                                               while(state!=2)  
                                                               {                                                               
                                                                 cw(1);   
                                                               }
                                                               cw(200);
                                                               Stop();
                                                               setLED(255,255,255);
                                                               digitalWrite(4,LOW);              //关光耦灯
                                                               break;
                                              }
                                                   
                                             else if(command ==3 && state != 3)          //在第4个滤镜位置停
                                             {
                                                              n = 0; ccw(500);
                                                              digitalWrite(4,HIGH);
                                                              setLED(254,255,254);
                                                              n = 1;
                                                              while(state!=3)  
                                                              {                                                              
                                                                cw(1);   
                                                              }
                                                              cw(200);
                                                              Stop();
                                                              setLED(255,255,255);
                                                              digitalWrite(4,LOW);              //关光耦灯
                                                              break;
                                              }
                                                   
                                              else if(command ==4 && state != 4)          //在第5个滤镜位置停
                                              {
                                                              n = 0; ccw(500);
                                                              digitalWrite(4,HIGH);
                                                              setLED(255,254,254);
                                                              n = 1;
                                                              while(state!=4)
                                                              {                                                              
                                                                cw(1);   
                                                              }
                                                              cw(200);
                                                              Stop();
                                                              setLED(255,255,255);
                                                              digitalWrite(4,LOW);              //关光耦灯
                                                              break;
                                              }
                
                                             else if(command ==5 && state != 5)          //在第6个滤镜位置停
                                             {
                                                              n = 0; ccw(500);
                                                              digitalWrite(4,HIGH);
                                                              setLED(254,254,255);
                                                              n = 1;
                                                              while(state!=5)
                                                              {   
                                                                cw(1);   
                                                              }
                                                              cw(200);
                                                              Stop();
                                                              setLED(255,255,255);
                                                              digitalWrite(4,LOW);              //关光耦灯
                                                              break;
                                             }
                                                       
                                            else if(command ==6 && state != 6)          //在第7个滤镜位置停
                                            {
                                                              n = 0; ccw(500);
                                                              digitalWrite(4,HIGH);
                                                              setLED(254,254,254);
                                                              n = 1;
                                                              while(state!=6)
                                                              {                                                            
                                                                cw(1);   
                                                              }
                                                              cw(200);
                                                              Stop();
                                                              setLED(255,255,255);
                                                              digitalWrite(4,LOW);              //关光耦灯
                                                              break;
                                            } 
                                                       else if(command ==7 && state != 7)          //在第8个滤镜位置停
                                                       {
                                                              n = 0; ccw(500);
                                                              digitalWrite(4,HIGH);
                                                              setLED(253,255,253);
                                                              n = 1;
                                                              while(state!=7)
                                                              {                                                            
                                                                cw(1);   
                                                              }
                                                              cw(200);
                                                              Stop();
                                                              setLED(255,255,255);
                                                              digitalWrite(4,LOW);              //关光耦灯
                                                              break;
                                                       }
                                                       else if(command ==8 && state != 8)          //在第9个滤镜位置停
                                                       {
                                                              n = 0; ccw(500);
                                                              digitalWrite(4,HIGH);
                                                              setLED(255,253,253);
                                                              n = 1;
                                                              while(state!=8)
                                                              {                                                            
                                                                cw(1);   
                                                              }
                                                              cw(200);
                                                              Stop();
                                                              setLED(255,255,255);
                                                              digitalWrite(4,LOW);              //关光耦灯
                                                              break;
                                                       }
                                                       else if(command ==9 && state != 9)          //在第10个滤镜位置停
                                                       {
                                                              n = 0; ccw(500);
                                                              digitalWrite(4,HIGH);
                                                              setLED(253,253,255);
                                                              n = 1;
                                                              while(state!=9)
                                                              {                                                            
                                                                cw(1);   
                                                              }
                                                              cw(200);
                                                              Stop();
                                                              setLED(255,255,255);
                                                              digitalWrite(4,LOW);              //关光耦灯
                                                              break;
                                                       }
                                                       else if(command ==10 && state != 10)          //在第11个滤镜位置停
                                                       {
                                                              n = 0; ccw(500);
                                                              digitalWrite(4,HIGH);
                                                              setLED(253,253,253);
                                                              n = 1;
                                                              while(state!=10)
                                                              {                                                            
                                                                cw(1);   
                                                              }
                                                              cw(200);
                                                              Stop();
                                                              setLED(255,255,255);
                                                              digitalWrite(4,LOW);              //关光耦灯
                                                              break;
                                                       }
                                                       else if(command ==11 && state != 11)          //在第12个滤镜位置停
                                                       {
                                                              n = 0; ccw(500);
                                                              digitalWrite(4,HIGH);
                                                              setLED(252,255,252);
                                                              n = 1;
                                                              while(state!=11)
                                                              {                                                            
                                                                cw(1);   
                                                              }
                                                              cw(200);
                                                              Stop();
                                                              setLED(255,255,255);
                                                              digitalWrite(4,LOW);              //关光耦灯
                                                              break;
                                                       }
                                                       else if(command ==12 && state != 12)          //在第13个滤镜位置停
                                                       {
                                                              n = 0; ccw(500);
                                                              digitalWrite(4,HIGH);
                                                              setLED(255,252,252);
                                                              n = 1;
                                                              while(state!=12)
                                                              {                                                            
                                                                cw(1);   
                                                              }
                                                              cw(200);
                                                              Stop();
                                                              setLED(255,255,255);
                                                              digitalWrite(4,LOW);              //关光耦灯
                                                              break;
                                                       }
                                                       else if(command ==13 && state != 13)          //在第14个滤镜位置停
                                                       {
                                                              n = 0; ccw(500);
                                                              digitalWrite(4,HIGH);
                                                              setLED(252,252,255);
                                                              n = 1;
                                                              while(state!=13)
                                                              {                                                            
                                                                cw(1);   
                                                              }
                                                              cw(200);
                                                              Stop();
                                                              setLED(255,255,255);
                                                              digitalWrite(4,LOW);              //关光耦灯
                                                              break;
                                                       }
                                                       else if(command ==14 && state != 14)          //在第15个滤镜位置停
                                                       {
                                                              n = 0; ccw(500);
                                                              digitalWrite(4,HIGH);
                                                              setLED(252,252,252);
                                                              n = 1;
                                                              while(state!=14)
                                                              {                                                            
                                                                cw(1);   
                                                              }
                                                              cw(200);
                                                              Stop();
                                                              setLED(255,255,255);
                                                              digitalWrite(4,LOW);              //关光耦灯
                                                              break;
                                                       }
                                                       else if(command ==15 && state != 15)          //在第16个滤镜位置停
                                                       {
                                                              n = 0; ccw(500);
                                                              digitalWrite(4,HIGH);
                                                              setLED(251,255,251);
                                                              n = 1;
                                                              while(state!=15)
                                                              {                                                            
                                                                cw(1);   
                                                              }
                                                              cw(200);
                                                              Stop();
                                                              setLED(255,255,255);
                                                              digitalWrite(4,LOW);              //关光耦灯
                                                              break;
                                                       }
                                                       break;
                                               }       
                                           }
                                      }
                                 }   
}

/*串口相机控制*/
void RXdata()
{     
             
             if(readdata[0] == '0' && state == 0){Serial.print(state);readdata = "";}          //在位置1，再次接收到字符0，返回参数0
             else if(readdata[0] == '0' && state !=  0)       //如果读到字符0,在第1个滤镜位置停
             {
                    n = 0; ccw(500);
                    digitalWrite(4,HIGH);
                    setLED(255,255,254);
                    n = 1;
                    if(sw == 1)
                    {
                        while(state!=0)                    
                        {
                            rst();
                            state = 0;
                        }
                    }
                    else if(sw == 0)
                    {
                        while(state!=0) 
                        {                                                 
                              cw(1);   
                        }
                    }
                    setLED(255,255,255);
                    digitalWrite(4,LOW);
                    Serial.print(state);
                    readdata = ""; 
             }
             else if(readdata[0] == '1' && state == 1){Serial.print(state);readdata = "";}                    
             else if(readdata[0]=='1' && state !=  1)         //如果读到字符1
             {   
                    n = 0; ccw(500);
                    digitalWrite(4,HIGH);
                    setLED(255,254,255);
                    n = 1;
                    while(state!=1) 
                    {                                                 
                          cw(1);   
                    }
                    cw(200);
                    Stop();         //在第2个滤镜位置停
                    setLED(255,255,255);     
                    digitalWrite(4,LOW);
                    Serial.print(state);
                    readdata = ""; 
             }   
             else if(readdata[0] == '2' && state == 2){Serial.print(state);readdata = "";}
             else if(readdata[0]=='2' && state !=  2)          //如果读到字符2
             {   
                    n = 0; ccw(500);
                    digitalWrite(4,HIGH);
                    setLED(254,255,255);
                    n = 1;
                    while(state!=2) 
                    {                                                 
                          cw(1);   
                    }
                    cw(200);
                    Stop();        //在第3个滤镜位置停
                    setLED(255,255,255);      
                    digitalWrite(4,LOW);
                    Serial.print(state);
                    readdata = ""; 
             }
             else if(readdata[0] == '3' && state == 3){Serial.print(state);readdata = "";}
             else  if(readdata[0]=='3' && state != 3)           //如果读到字符3
             {  
                    n = 0; ccw(500);
                    digitalWrite(4,HIGH);
                    setLED(254,255,254);
                    n = 1;
                    while(state!=3) 
                    {                                               
                            cw(1);   
                    }
                    cw(200);
                    Stop();         //在第4个滤镜位置停
                    setLED(255,255,255);
                    digitalWrite(4,LOW);
                    Serial.print(state);
                    readdata = ""; 
             }
             else if(readdata[0] == '4' && state == 4){Serial.print(state);readdata = "";}
             else if(readdata[0]=='4' && state != 4)          //如果读到字符4
             {
                     n = 0; ccw(500);
                     digitalWrite(4,HIGH);
                     setLED(255,254,254);
                     n = 1;
                     while(state!=4) 
                     {       
                             cw(1);  
                     }
                     cw(200);
                     Stop();       //在第5个滤镜位置停
                     setLED(255,255,255);           
                     digitalWrite(4,LOW);
                     Serial.print(state);
                     readdata = ""; 
              }
              else if(readdata[0] == '5' && state == 5){Serial.print(state);readdata = "";}
              else if(readdata[0]=='5' && state != 5)          //如果读到字符5
              {
                     n = 0; ccw(500);
                     digitalWrite(4,HIGH);
                     setLED(254,254,255);
                     n = 1;
                     while(state!=5) 
                     {        
                            cw(1);  
                     }
                     cw(200);
                     Stop();        //在第6个滤镜位置停
                     setLED(255,255,255);           
                     digitalWrite(4,LOW);
                     Serial.print(state);
                     readdata = ""; 
               }
               else if(readdata[0] == '6' && state == 6){Serial.print(state);readdata = "";}
               else if(readdata[0]=='6' && state != 6)        //如果读到字符6
               {
                     n = 0; ccw(500);
                     digitalWrite(4,HIGH);
                     setLED(254,254,254);
                     n = 1;
                     while(state!=6) 
                     {       
                             cw(1);  
                     }
                     cw(200);
                     Stop();         //在第7个滤镜位置停
                     setLED(255,255,255);            
                     digitalWrite(4,LOW);
                     Serial.print(state);
                     readdata = ""; 
                }
               else if(readdata[0] == '7' && state == 7){Serial.print(state);readdata = "";} 
               else if(readdata[0]=='7' && state != 7)          //如果读到字符7
               {
                     n = 0; ccw(500);
                     digitalWrite(4,HIGH);
                     setLED(255,253,253);
                     n = 1;
                     while(state!=7) 
                     {       
                             cw(1);  
                     }
                     cw(200);
                     Stop();       //在第8个滤镜位置停
                     setLED(255,255,255);            
                     digitalWrite(4,LOW);
                     Serial.print(state);
                     readdata = ""; 
                }
               else if(readdata[0] == '8' && state == 8){Serial.print(state);readdata = "";}
               else if(readdata[0]=='8' && state != 8)        //如果读到字符8
               {
                     n = 0; ccw(500);
                     digitalWrite(4,HIGH);
                     setLED(253,255,253);
                     n = 1;
                     while(state!=8) 
                     {       
                             cw(1);  
                     }
                     cw(200);
                     Stop();       //在第9个滤镜位置停
                     setLED(255,255,255);            
                     digitalWrite(4,LOW);
                     Serial.print(state);
                     readdata = ""; 
                }
               else if(readdata[0] == '9' && state == 9){Serial.print(state);readdata = "";} 
               else if(readdata[0]=='9' && state != 9)         //如果读到字符9
               {
                     n = 0; ccw(500);
                     digitalWrite(4,HIGH);
                     setLED(253,253,255);
                     n = 1;
                     while(state!=9) 
                     {       
                             cw(1);  
                     }
                     cw(200);
                     Stop();       //在第10个滤镜位置停
                     setLED(255,255,255);          
                     digitalWrite(4,LOW);
                     Serial.print(state);
                     readdata = ""; 
                }
               else if(readdata[0] == 'A' && state == 10){Serial.print(state);readdata = "";} 
               else if(readdata[0]=='A' && state != 10)       //如果读到字符A
               {
                     n = 0; ccw(500);
                     digitalWrite(4,HIGH);
                     setLED(253,253,253);
                     n = 1;
                     while(state!=10) 
                     {       
                             cw(1);  
                     }
                     cw(200);
                     Stop();      //在第11个滤镜位置停
                     setLED(255,255,255);          
                     digitalWrite(4,LOW);
                     Serial.print(state);
                     readdata = ""; 
                }
               else if(readdata[0] == 'B' && state == 11){Serial.print(state);readdata = "";} 
               else if(readdata[0]=='B' && state != 11)        //如果读到字符B
               {
                     n = 0; ccw(500);
                     digitalWrite(4,HIGH);
                     setLED(252,255,252);
                     n = 1;
                     while(state!=11) 
                     {       
                             cw(1);  
                     }
                     cw(200);
                     Stop();         //在第12个滤镜位置停
                     setLED(255,255,255);          
                     digitalWrite(4,LOW);
                     Serial.print(state);
                     readdata = ""; 
                }
                else if(readdata[0] == 'C' && state == 12){Serial.print(state);readdata = "";}
                else if(readdata[0]=='C' && state != 12)           //如果读到字符C
                {
                     n = 0; ccw(500);
                     digitalWrite(4,HIGH);
                     setLED(255,252,252);
                     n = 1;
                     while(state!=12) 
                     {       
                             cw(1);  
                     }
                     cw(200);
                     Stop();          //在第13个滤镜位置停
                     setLED(255,255,255);           
                     digitalWrite(4,LOW);
                     Serial.print(state);
                     readdata = "";
                }
               else if(readdata[0] == 'D' && state == 13){Serial.print(state);readdata = "";} 
               else if(readdata[0]=='D' && state != 13)         //如果读到字符D
               {
                     n = 0; ccw(500);
                     digitalWrite(4,HIGH);
                     setLED(252,252,255);
                     n = 1;
                     while(state!=13) 
                     {       
                             cw(1);  
                     }
                     cw(200);
                     Stop();            //在第14个滤镜位置停
                     setLED(255,255,255);          
                     digitalWrite(4,LOW);
                     Serial.print(state);
                     readdata = ""; 
                }
               else if(readdata[0] == 'E' && state == 14){Serial.print(state);readdata = "";} 
               else if(readdata[0]=='E' && state != 14)                 //如果读到字符E
               {
                     n = 0; ccw(500);
                     digitalWrite(4,HIGH);  
                     setLED(252,252,252);
                     n = 1;
                     while(state!=14) 
                     {       
                             cw(1);  
                     }
                     cw(200);
                     Stop();                     //在第15个滤镜位置停
                     setLED(255,255,255);          
                     digitalWrite(4,LOW);
                     Serial.print(state);
                     readdata = "";     
                }
               else if(readdata[0] == 'F' && state == 15){Serial.print(state);readdata = "";} 
               else if(readdata[0]=='F' && state != 15)                 //如果读到字符15
               {
                     n = 0; ccw(500);
                     digitalWrite(4,HIGH);
                     setLED(251,255,251);
                     n = 1;
                     while(state!=15) 
                     {       
                             cw(1);  
                     }
                     cw(200);
                     Stop();                     //在第16个滤镜位置停
                     setLED(255,255,255);           
                     digitalWrite(4,LOW);
                     Serial.print(state);
                     readdata = "";
                }        
}
void reset()
{
        digitalWrite(4,HIGH);  //开光耦
        rst();
        hole = 0;              //转盘孔数归零
        state = 0;             //转盘定位孔数归零
        rst();
        hole = state;
        digitalWrite(4,LOW);        
        state = 0;
        LED_flash();
       // Serial.print(state);
}

void loop()
{ 
        sw = 0;
        if(digitalRead(5)==HIGH)  s=1;
        else if(digitalRead(5)==LOW) s=0;
        digitalWrite(6,HIGH);
        digitalWrite(8,LOW);       //驱动器输出大电流
        setLED(254,254,254);
        digitalWrite(4,HIGH);      //开光耦
        rst();
        hole = 0;                  //转盘孔数归零
        state = 0;                //转盘定位孔数归零
        rst();
        hole = state;
        digitalWrite(4,LOW);        
        state = 0;
        LED_flash();
        Serial.print(state);
        while(1)
        {    
                   if (digitalRead(13)==LOW)           //QHY9相机控制函数
                   {   
                	  Q9cam();	
                   }
                    
              if(digitalRead(5)==HIGH && s ==1) //USB MODE
              {
                  setLED(250,255,255); //R
                  delay(1000);
                  setLED(255,255,255);
                  s = 0;
              }
              if(digitalRead(5)==LOW && s ==0) //Serial port MODE
              {
                  setLED(255,250,255); //G
                  delay(1000);
                  setLED(255,255,255);
                  s = 1;
              }
                   if(Serial.available() > 0)                         //串口相机控制函数                                    
                   {
                           readdata += char(Serial.read());
                           delay(2);                          
                   }
                   else if(readdata.length() < 2)
                   {
                           
                           if((readdata[0] <= '9'&& readdata[0]-47 < hole)||(readdata[0]>= 'A'&&readdata[0]-54<hole))
                           {     
                                   //正反转判断
                                   if(((state - readdata[0]+48)>0 &&(state - readdata[0]+48) < hole/2)||(readdata[0]-48 - state) > hole/2) //||((state - readdata[0]+55) > 0 &&(state - readdata[0]+55) < hole/2)||(readdata[0]-55 - state) > hole/2)
                                  {
                                      digitalWrite(6,HIGH);       //反转             
                                      sw = 0;
                                  }
                                  else
                                  {
                                      digitalWrite(6,HIGH);       //正转
                                      sw = 0;
                                  }                             
                                   RXdata();   
                                                             
                           } 
                           else
                           {
                                  readdata = "";
                           } 
                   }         
                   else if(readdata.length()>1)
                   {       
                           if(readdata == "VRS")                         
                           {
                                   Serial.print("20181114");          //输出FW版本号
                                   readdata = "";
                           }
                           else if(readdata == "MXP")
                           {
                                   Serial.print(hole-1);              //输出转盘孔数
                                   readdata = "";
                           }              
                           else if(readdata == "NOW")
                           {
                                   Serial.print(state);             //输出转盘当前位置
                                   readdata = "";
                           } 
                           else if(readdata == "RESET")
                           {
                                 reset();
                                 readdata = "";
                           }
                           else
                           {
                                  readdata = "" ;
                           }  
                  }                   
        }
}
      
